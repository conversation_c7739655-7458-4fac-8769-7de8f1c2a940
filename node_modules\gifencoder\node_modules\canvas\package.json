{"name": "canvas", "description": "Canvas graphics API backed by Cairo", "version": "2.11.2", "author": "<PERSON><PERSON> <<EMAIL>>", "main": "index.js", "browser": "browser.js", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON>agg <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>"], "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "homepage": "https://github.com/Automattic/node-canvas", "repository": "git://github.com/Automattic/node-canvas.git", "scripts": {"prebenchmark": "node-gyp build", "benchmark": "node benchmarks/run.js", "lint": "standard examples/*.js test/server.js test/public/*.js benchmarks/run.js lib/context2d.js util/has_lib.js browser.js index.js", "test": "mocha test/*.test.js", "pretest-server": "node-gyp build", "test-server": "node test/server.js", "generate-wpt": "node ./test/wpt/generate.js", "test-wpt": "mocha test/wpt/generated/*.js", "install": "node-pre-gyp install --fallback-to-build --update-binary", "dtslint": "dtslint types"}, "binary": {"module_name": "canvas", "module_path": "build/Release", "host": "https://github.com/Automattic/node-canvas/releases/download/", "remote_path": "v{version}", "package_name": "{module_name}-v{version}-{node_abi}-{platform}-{libc}-{arch}.tar.gz"}, "files": ["binding.gyp", "lib/", "src/", "util/", "types/index.d.ts"], "types": "types/index.d.ts", "dependencies": {"@mapbox/node-pre-gyp": "^1.0.0", "nan": "^2.17.0", "simple-get": "^3.0.3"}, "devDependencies": {"@types/node": "^10.12.18", "assert-rejects": "^1.0.0", "dtslint": "^4.0.7", "express": "^4.16.3", "js-yaml": "^4.1.0", "mocha": "^5.2.0", "pixelmatch": "^4.0.2", "standard": "^12.0.1", "typescript": "^4.2.2"}, "engines": {"node": ">=6"}, "license": "MIT"}