const {
  Client,
  GatewayIntentBits,
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
  EmbedBuilder,
  InteractionType,
  MessageFlags
} = require('discord.js');
const { createCanvas, loadImage } = require('canvas');
const GIFEncoder = require('gifencoder');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

const client = new Client({
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildMessages,
    GatewayIntentBits.MessageContent,
    GatewayIntentBits.GuildMembers
  ]
});

const POINTS_FILE = path.join(__dirname, 'points.json');
const SHOP_ITEMS = [
  { id: 'immunity', name: 'حصانة', description: 'تحميك من الطرد في الجولة القادمة', price: 15, emoji: '🛡️' },
  { id: 'double', name: 'مضاعفة', description: 'تضاعف نقاطك إذا فزت في الجولة القادمة', price: 20, emoji: '✖️' },
  { id: 'reroll', name: 'إعادة تدوير', description: 'تسمح لك بإعادة اختيار شخص آخر إذا كنت المختار', price: 25, emoji: '🔄' }
];
const MAX_SEATS = 6;
const SEAT_TIMEOUT = 30 * 1000;
const activeGames = new Map();

// نظام النقاط
function loadPoints() {
  if (!fs.existsSync(POINTS_FILE)) return {};
  const data = JSON.parse(fs.readFileSync(POINTS_FILE));
  for (const userId in data) {
    data[userId] = {
      points: data[userId].points || 0,
      wins: data[userId].wins || 0,
      streak: data[userId].streak || 0,
      lastWin: data[userId].lastWin || null,
      items: data[userId].items || []
    };
  }
  return data;
}

function savePoints(data) {
  fs.writeFileSync(POINTS_FILE, JSON.stringify(data, null, 2));
}

function addPoints(userId, amount) {
  const data = loadPoints();
  if (!data[userId]) {
    data[userId] = { points: 0, wins: 0, streak: 0, lastWin: null, items: [] };
  }

  data[userId].points += amount;
  data[userId].wins += 1;
  data[userId].lastWin = new Date().toISOString();
  savePoints(data);

  return {
    points: amount
  };
}

// دوال المساعدة
function buildSeatRows(seatMap) {
  const row1 = new ActionRowBuilder();
  const row2 = new ActionRowBuilder();
  const row3 = new ActionRowBuilder(); // صف جديد لزر الخروج

  // أزرار المقاعد
  for (let i = 1; i <= 3; i++) {
    const occ = seatMap.get(i);
    row1.addComponents(
      new ButtonBuilder()
        .setCustomId(`seat_${i}`)
        .setLabel(occ ? occ.name : `مقعد ${i}`)
        .setStyle(occ ? ButtonStyle.Success : ButtonStyle.Secondary)
        .setDisabled(Boolean(occ))
    );
  }
  for (let i = 4; i <= 6; i++) {
    const occ = seatMap.get(i);
    row2.addComponents(
      new ButtonBuilder()
        .setCustomId(`seat_${i}`)
        .setLabel(occ ? occ.name : `مقعد ${i}`)
        .setStyle(occ ? ButtonStyle.Success : ButtonStyle.Secondary)
        .setDisabled(Boolean(occ))
    );
  }

  // إضافة زر الخروج
  row3.addComponents(
    new ButtonBuilder()
      .setCustomId('exit_game')
      .setLabel('خروج من اللعبة')
      .setStyle(ButtonStyle.Danger)
      .setEmoji('🚪')
  );

  return [row1, row2, row3];
}

function buildTargetRows(seatMap, pickerId) {
  const rows = [];
  let row = new ActionRowBuilder();
  let count = 0;

  // إضافة أزرار الطرد للاعبين
  for (const [seatNum, occ] of seatMap.entries()) {
    if (occ.id === pickerId) continue;
    if (count === 3) {
      rows.push(row);
      row = new ActionRowBuilder();
      count = 0;
    }
    row.addComponents(
      new ButtonBuilder()
        .setCustomId(`kick_${seatNum}`)
        .setLabel(occ.name)
        .setStyle(ButtonStyle.Danger)
    );
    count++;
  }
  if (row.components.length) rows.push(row);

  // إضافة الأزرار الخاصة في صف واحد
  const remainingPlayers = Array.from(seatMap.values()).filter(occ => occ.id !== pickerId);
  const specialRow = new ActionRowBuilder();

  // إضافة زر الانسحاب دائماً
  specialRow.addComponents(
    new ButtonBuilder()
      .setCustomId('leave_game')
      .setLabel('انسحاب')
      .setStyle(ButtonStyle.Danger)
      .setEmoji('🚪')
  );

  // إضافة زر الطرد العشوائي فقط إذا كان هناك أكثر من لاعبين
  if (remainingPlayers.length > 1) {
    specialRow.addComponents(
      new ButtonBuilder()
        .setCustomId('random_kick')
        .setLabel('طرد عشوائي')
        .setStyle(ButtonStyle.Primary)
        .setEmoji('🎲')
    );
  }

  rows.push(specialRow);
  return rows;
}

// دالة حذف الملفات المؤقتة
async function deleteWheelFiles(messageId) {
  const gifPath = path.join(__dirname, `wheel_${messageId}.gif`);
  const finalImagePath = path.join(__dirname, `wheel_${messageId}_final.png`);

  try {
    if (fs.existsSync(gifPath)) {
      fs.unlinkSync(gifPath);
    }
    if (fs.existsSync(finalImagePath)) {
      fs.unlinkSync(finalImagePath);
    }
  } catch (error) {
    console.error('خطأ في حذف الملفات المؤقتة:', error);
  }
}

// توليد الصور
async function generateWheelGif(names, selectedIndex, gifPath) {
  const size = 500;
  const encoder = new GIFEncoder(size, size);
  const stream = fs.createWriteStream(gifPath);
  encoder.createReadStream().pipe(stream);

  encoder.start();
  encoder.setRepeat(0);
  encoder.setQuality(10);
  encoder.setTransparent(0x000000);
  encoder.setDispose(2);

  const canvas = createCanvas(size, size);
  const ctx = canvas.getContext('2d');

  const segments = names.length;
  const anglePerSegment = (2 * Math.PI) / segments;
  const fullSpins = 3;
  const finalRotation = (fullSpins * 2 * Math.PI) + ((0) - (selectedIndex * anglePerSegment + anglePerSegment / 2));
  const totalFrames = 60;

  for (let i = 0; i < totalFrames; i++) {
    const t = i / (totalFrames - 1);
    const eased = 1 - Math.pow(1 - t, 4);
    const currentRotation = finalRotation * eased;
    const delay = 10 + Math.floor(30 * Math.pow(t, 2));
    drawWheelFrame(ctx, size, names, currentRotation);
    encoder.setDelay(delay);
    encoder.addFrame(ctx);
  }

  encoder.finish();

  const finalImagePath = gifPath.replace('.gif', '_final.png');
  const finalRotationStatic = (0) - (selectedIndex * anglePerSegment + anglePerSegment / 2);
  drawWheelFrame(ctx, size, names, finalRotationStatic);
  const buffer = canvas.toBuffer('image/png');
  fs.writeFileSync(finalImagePath, buffer);

  return new Promise(resolve => stream.on('close', () => resolve(finalImagePath)));
}

function drawWheelFrame(ctx, size, names, rotation) {
  ctx.clearRect(0, 0, size, size);

  const segments = names.length;
  const anglePerSegment = (2 * Math.PI) / segments;

  names.forEach((name, i) => {
    const startAngle = i * anglePerSegment + rotation;
    const endAngle = startAngle + anglePerSegment;

    ctx.beginPath();
    ctx.moveTo(size / 2, size / 2);
    ctx.arc(size / 2, size / 2, size / 2 - 20, startAngle, endAngle);
    ctx.closePath();
    // مصفوفة من الألوان المريحة للعين
    const colors = [
      '#a8e6cf', // أخضر فاتح
      '#dcedc1', // أخضر مصفر
      '#ffd3b6', // برتقالي فاتح
      '#ffaaa5', // وردي فاتح
      '#ff8b94', // وردي
      '#bde4ff', // أزرق فاتح
      '#e8e8e8', // رمادي فاتح
      '#d4f0f0'  // تركواز فاتح
    ];

    // تلوين القطاع
    ctx.fillStyle = colors[i % colors.length]; // استخدام الألوان بالتناوب
    ctx.fill();

    // رسم الخط الفاصل الأسود
    ctx.beginPath();
    ctx.moveTo(size / 2, size / 2);
    ctx.lineTo(
      size / 2 + Math.cos(endAngle) * (size / 2 - 20),
      size / 2 + Math.sin(endAngle) * (size / 2 - 20)
    );
    ctx.lineWidth = 2;
    ctx.strokeStyle = '#333333';
    ctx.stroke();

    const textAngle = (startAngle + endAngle) / 2;
    ctx.save();
    ctx.translate(size / 2, size / 2);
    ctx.rotate(textAngle);
    ctx.textAlign = 'center';
    ctx.font = 'bold 20px Arial';
    ctx.lineWidth = 3;
    ctx.strokeStyle = 'black';
    ctx.strokeText(name, size / 3, 0);
    ctx.fillStyle = 'white';
    ctx.fillText(name, size / 3, 0);
    ctx.restore();
  });

  ctx.fillStyle = 'red';
  ctx.beginPath();
  ctx.moveTo(size - 10, size / 2);
  ctx.lineTo(size - 40, size / 2 - 20);
  ctx.lineTo(size - 40, size / 2 + 20);
  ctx.closePath();
  ctx.fill();
}

async function generateWelcomeImage(guild) {
  const width = 500;
  const height = 300;
  const canvas = createCanvas(width, height);
  const ctx = canvas.getContext('2d', { alpha: true });
  ctx.clearRect(0, 0, width, height); // مسح الخلفية لجعلها شفافة


  // رسم صورة السيرفر
  const guildIcon = guild.iconURL({ extension: 'png', size: 128 });
  if (guildIcon) {
    const icon = await loadImage(guildIcon);
    const iconSize = 100;
    const iconX = (width - iconSize) / 2;
    ctx.save();
    ctx.beginPath();
    ctx.arc(width / 2, 70, iconSize / 2, 0, Math.PI * 2, true);
    ctx.closePath();
    ctx.clip();
    ctx.drawImage(icon, iconX, 20, iconSize, iconSize);
    ctx.restore();
  }

  // كتابة اسم السيرفر
  ctx.fillStyle = '#ffffff';
  ctx.font = 'bold 28px Arial';
  ctx.textAlign = 'center';
  ctx.fillText(guild.name, width / 2, 160);

  // كتابة الرسالة
  ctx.font = 'bold 24px Arial';
  ctx.fillText('الرجاء اختيار أحد المقاعد للانضمام', width / 2, 210);

  ctx.font = '20px Arial';
  ctx.fillStyle = '#cccccc';
  ctx.fillText('لديكم 30 ثانية للانضمام للعبة', width / 2, 250);

  return canvas.toBuffer('image/png');
}

async function generateWinnerImage(user) {
  const width = 400;
  const height = 180;
  const canvas = createCanvas(width, height);
  const ctx = canvas.getContext('2d');

  const avatarURL = user.displayAvatarURL({ extension: 'png', size: 128 });
  const avatar = await loadImage(avatarURL);

  const avatarX = 20;
  const avatarY = 20;
  const avatarSize = 140;
  ctx.save();
  ctx.beginPath();
  ctx.arc(avatarX + avatarSize / 2, avatarY + avatarSize / 2, avatarSize / 2, 0, Math.PI * 2, true);
  ctx.closePath();
  ctx.clip();

  ctx.drawImage(avatar, avatarX, avatarY, avatarSize, avatarSize);
  ctx.restore();

  ctx.fillStyle = '#FFFFFF';
  ctx.font = 'bold 28px Arial';
  ctx.textAlign = 'left';
  ctx.fillText(user.displayName || user.username, avatarX + avatarSize + 20, height / 2 + 10);

  return canvas.toBuffer('image/png');
}

// دالة إنهاء مرحلة حجز المقاعد
async function endSeatPhase(messageId) {
  const game = activeGames.get(messageId);
  if (!game) return;

  const channel = await client.channels.fetch(game.channelId);
  const msg = await channel.messages.fetch(messageId);

  if (game.seatMap.size === 0) {
    await msg.edit({
      content: '❌ لا يوجد لاعبين لبدء اللعبة',
      components: []
    });
    activeGames.delete(messageId);
    return;
  }

  if (game.seatMap.size === 1) {
    await msg.edit({
      content: '❌ لا يوجد عدد لاعبين كافي للبدء',
      components: []
    });
    activeGames.delete(messageId);
    return;
  }

  if (game.seatMap.size > 1) {
    await channel.send('✅ تم جمع اللاعبين وسيتم البدء خلال ثوان');
  }

  await startRound(game, msg);
}

// أحداث البوت
client.once('ready', () => {
  console.log(`✅ Logged in as ${client.user.tag}`);
});

client.on('messageCreate', async message => {
  if (message.author.bot) return;

  // تحويل الرسالة إلى أحرف صغيرة وإزالة المسافات الزائدة
  const content = message.content.trim().toLowerCase();

  if (content === 'روليت' || content === '!روليت') {
    const seatMap = new Map();
    const rows = buildSeatRows(seatMap);
    const welcomeImageBuffer = await generateWelcomeImage(message.guild);
    const invite = await message.channel.send({
      content: '🎲 **روليت الجماعة**',
      files: [{ attachment: welcomeImageBuffer, name: 'welcome.png' }],
      components: rows
    });

    const timeout = setTimeout(() => endSeatPhase(invite.id), SEAT_TIMEOUT);
    activeGames.set(invite.id, { channelId: message.channel.id, seatMap, timeout });
    return;
  }

  if (content === 'نقاطي' || content === '!نقاطي') {
    const data = loadPoints();
    const userData = data[message.author.id] || { points: 0, wins: 0, streak: 0 };
    message.reply(`🎯 نقاطك: ${userData.points}\n🏆 فوزاتك: ${userData.wins}\n🔥 المكاسب المتتالية: ${userData.streak}`);
    return;
  }

  if (content === 'المتجر' || content === '!المتجر') {
    const items = SHOP_ITEMS.map((item, index) =>
      `${index + 1}. ${item.emoji} **${item.name}** - ${item.price} نقطة`
    ).join('\n');
    message.reply(`🛒 **المتجر**\n${items}\n\nللشراء: شراء [رقم العنصر]`);
    return;
  }

  if (content.startsWith('شراء') || content.startsWith('!شراء')) {
    const parts = message.content.trim().split(' ');
    const itemNum = parseInt(parts[1]);
    if (isNaN(itemNum)) {
      return message.reply('❗ الرجاء تحديد رقم العنصر');
    }

    const item = SHOP_ITEMS[itemNum - 1];
    if (!item) {
      return message.reply('❗ هذا العنصر غير موجود');
    }

    const data = loadPoints();
    const userData = data[message.author.id] || { points: 0, items: [] };

    if (userData.points < item.price) {
      return message.reply(`❗ ليس لديك نقاط كافية. تحتاج ${item.price} نقطة`);
    }

    userData.items = userData.items || [];
    userData.items.push(item.id);
    userData.points -= item.price;

    savePoints(data);
    message.reply(`✅ لقد اشتريت ${item.emoji} **${item.name}**! استخدم \`عناصري\` لرؤية عناصرك`);
    return;
  }

  if (content === 'عناصري' || content === '!عناصري') {
    const data = loadPoints();
    const userData = data[message.author.id] || { points: 0, items: [] };

    if (!userData.items || userData.items.length === 0) {
      return message.reply('❗ لا تملك عناصر');
    }

    const itemsList = userData.items.map(itemId => {
      const item = SHOP_ITEMS.find(i => i.id === itemId);
      return item ? `${item.emoji} ${item.name}` : 'عنصر غير معروف';
    }).join('\n');

    message.reply(`🎒 **عناصرك**\n${itemsList}`);
    return;
  }

  if (content === 'اوامر' || content === 'الاوامر' || content === '!اوامر') {
    const commands = [
      '🎲 **روليت** - بدء لعبة جديدة',
      '🎯 **نقاطي** - عرض نقاطك وإحصائياتك',
      '🛒 **المتجر** - عرض العناصر المتوفرة للشراء',
      '💰 **شراء [رقم العنصر]** - شراء عنصر من المتجر',
      '🎒 **عناصري** - عرض العناصر التي تملكها',
      '🏆 **الصدارة** - عرض أفضل 10 لاعبين'
    ].join('\n');

    message.reply(`📜 **قائمة الأوامر**\n${commands}\n\n💡 يمكنك استخدام الأوامر مع أو بدون علامة !`);
    return;
  }

  if (content === 'الصدارة' || content === '!الصدارة') {
    const data = loadPoints();
    const leaderboard = Object.entries(data)
      .map(([userId, userData]) => ({
        userId,
        points: userData.points || 0,
        wins: userData.wins || 0
      }))
      .sort((a, b) => b.points - a.points)
      .slice(0, 10);

    const members = await Promise.all(
      leaderboard.map(async item => {
        try {
          const user = await client.users.fetch(item.userId);
          return { ...item, username: user.username };
        } catch {
          return { ...item, username: 'مستخدم غير معروف' };
        }
      })
    );

    const leaderboardText = members.map((user, index) =>
      `${index + 1}. **${user.username}** - ${user.points} نقطة (${user.wins} فوز)`
    ).join('\n');

    message.reply(`🏆 **أفضل 10 لاعبين**\n${leaderboardText}`);
  }
});

client.on('interactionCreate', async interaction => {
  if (interaction.type !== InteractionType.MessageComponent) return;

  // التحقق من أن التفاعل لا يزال صالحاً
  if (!interaction.isRepliable()) {
    console.log('تفاعل غير قابل للرد عليه تم تجاهله');
    return;
  }

  try {
    if (interaction.customId === 'exit_game') {
      const game = activeGames.get(interaction.message.id);
      if (!game) {
        return interaction.reply({
          content: '❌ اللعبة غير موجودة',
          flags: MessageFlags.Ephemeral
        });
      }

      const userId = interaction.user.id;
      let userSeat = null;

      // البحث عن مقعد اللاعب
      for (const [seatNum, occ] of game.seatMap.entries()) {
        if (occ.id === userId) {
          userSeat = seatNum;
          break;
        }
      }

      if (userSeat === null) {
        return interaction.reply({
          content: '❌ ليس لديك مقعد في اللعبة',
          flags: MessageFlags.Ephemeral
        });
      }

      // إزالة اللاعب من مقعده
      game.seatMap.delete(userSeat);
      const updated = buildSeatRows(game.seatMap);
      await interaction.message.edit({ components: updated });

      await interaction.reply({
        content: '✅ تم الخروج من اللعبة بنجاح',
        flags: MessageFlags.Ephemeral
      });

      // لا يتم إنهاء اللعبة عند خروج لاعب حتى لو كان عدد اللاعبين أقل من المطلوب
      // يمكن تعديل هذا السلوك لاحقاً إذا رغبت
      // إذا أردت إنهاء اللعبة عند عدد لاعبين أقل، قم بإلغاء التعليق التالي:
      /*
      if (game.seatMap.size < 2) {
        await interaction.message.edit({
          content: '❌ عدد اللاعبين غير كافي',
          components: []
        });
        clearTimeout(game.timeout);
        activeGames.delete(interaction.message.id);
      }
      */
      return;
    }

    if (interaction.customId.startsWith('seat_')) {
      const game = activeGames.get(interaction.message.id);
      if (!game) {
        return interaction.reply({
          content: '❌ اللعبة غير موجودة',
          flags: MessageFlags.Ephemeral
        });
      }
      await interaction.deferUpdate();

      const userId = interaction.user.id;
      const member = await interaction.guild.members.fetch(userId);
      if ([...game.seatMap.values()].some(o => o.id === userId)) {
        return interaction.followUp({ content: '❌ لديك مقعد بالفعل', flags: MessageFlags.Ephemeral });
      }

      const seatNum = Number(interaction.customId.split('_')[1]);
      if (game.seatMap.has(seatNum)) {
        return interaction.followUp({ content: '❌ المقعد محجوز', flags: MessageFlags.Ephemeral });
      }

      game.seatMap.set(seatNum, { id: userId, name: member.displayName });
      const updated = buildSeatRows(game.seatMap);
      const channel = await client.channels.fetch(game.channelId);
      const msg = await channel.messages.fetch(interaction.message.id);
      await msg.edit({ components: updated });
      interaction.followUp({ content: `✅ مقعد ${seatNum}`, flags: MessageFlags.Ephemeral });

      if (game.seatMap.size === MAX_SEATS) {
        clearTimeout(game.timeout);
        endSeatPhase(interaction.message.id);
      }
      return;
    }

    if (interaction.customId.startsWith('reroll_')) {
      const userId = interaction.customId.split('_')[1];
      if (interaction.user.id !== userId) {
        return interaction.reply({ content: '❌ لا يمكنك استخدام هذا الزر!', flags: MessageFlags.Ephemeral });
      }
      await interaction.deferUpdate();
      return;
    }

    if (interaction.customId === 'leave_game') {
      const game = activeGames.get(interaction.message.id);
      if (!game) {
        return interaction.reply({
          content: '❌ اللعبة غير موجودة',
          flags: MessageFlags.Ephemeral
        });
      }

      const userId = interaction.user.id;

      // التحقق مما إذا كان المستخدم هو المختار في الجولة الحالية
      if (game.picker && game.picker.id === userId) {
        // إذا كان المستخدم هو المختار، نقوم بحذفه مباشرة
        for (const [seatNum, occ] of game.seatMap.entries()) {
          if (occ.id === userId) {
            game.seatMap.delete(seatNum);
            break;
          }
        }

        await interaction.deferUpdate();
        const channel = await client.channels.fetch(game.channelId);

        // إرسال رسالة الانسحاب
        await channel.send(`🚪 انسحب <@${userId}> من اللعبة!`);

        await channel.send('⏳ سيتم بدء الجولة القادمة خلال ثوان');

        // تحديث رسالة اللعبة
        const msg = await channel.messages.fetch(interaction.message.id);
        if (game.seatMap.size === 1) {
          // إعلان الفائز
          const winner = [...game.seatMap.values()][0];
          const user = await client.users.fetch(winner.id);
          const buffer = await generateWinnerImage(user);

          const data = loadPoints();
          const winnerData = data[winner.id] || { items: [] };
          let pointsToAdd = 2; // نقطتين عند الفوز

          if (winnerData.items.includes('double')) {
            pointsToAdd *= 2;
            winnerData.items = winnerData.items.filter(item => item !== 'double');
            savePoints(data);
          }

          const pointsResult = addPoints(winner.id, pointsToAdd, game.seatMap.size + 1);

          await channel.send({
            content: `🏆 الفائز <@${winner.id}> وحصل على نقطتين`,
            files: [{ attachment: buffer, name: 'winner.png' }]
          });

          activeGames.delete(interaction.message.id);
        } else if (game.seatMap.size < 2) {
          await msg.edit({
            content: '❌ عدد اللاعبين غير كافي',
            components: []
          });
          activeGames.delete(interaction.message.id);
        } else {
          // بدء جولة جديدة مباشرة
          setTimeout(() => startRound(game, msg), 1000);
        }
      } else {
        // إذا لم يكن المستخدم هو المختار
        return interaction.reply({
          content: '❌ لا يمكنك الانسحاب - فقط اللاعب المختار يمكنه الانسحاب في هذه المرحلة',
          flags: MessageFlags.Ephemeral
        });
      }
      return;
    }

    if (interaction.customId === 'random_kick') {
      const game = activeGames.get(interaction.message.id);
      if (!game || !game.picker) {
        return interaction.reply({
          content: '❌ اللعبة غير موجودة أو لا يوجد مختار',
          flags: MessageFlags.Ephemeral
        });
      }

      if (interaction.user.id !== game.picker.id) {
        return interaction.reply({ content: '❗ ليس دورك للاختيار.', flags: MessageFlags.Ephemeral });
      }

      await interaction.deferUpdate();

      // اختيار لاعب عشوائي للطرد (باستثناء المختار)
      const players = Array.from(game.seatMap.entries())
        .filter(([_, occ]) => occ.id !== game.picker.id);

      if (players.length === 0) return;

      const [seatNum, victim] = players[Math.floor(Math.random() * players.length)];
      const channel = await client.channels.fetch(game.channelId);

      // حذف الضحية من الخريطة
      game.seatMap.delete(seatNum);

      // تحديث رسالة اللعبة وإرسال رسالة منفصلة للإقصاء
      const msg = await channel.messages.fetch(interaction.message.id);
      await msg.edit({
        content: '🎲 تم الاختيار العشوائي',
        components: []
      });

      await channel.send({
        content: `❌ تم طرد <@${victim.id}> من اللعبة بشكل عشوائي!`
      });

      if (game.seatMap.size === 1) {
        // إعلان الفائز
        const winner = [...game.seatMap.values()][0];
        const user = await client.users.fetch(winner.id);
        const buffer = await generateWinnerImage(user);

        const data = loadPoints();
        const winnerData = data[winner.id] || { items: [] };
        let pointsToAdd = 2; // نقطتين عند الفوز

        if (winnerData.items.includes('double')) {
          pointsToAdd *= 2;
          winnerData.items = winnerData.items.filter(item => item !== 'double');
          savePoints(data);
        }

        const pointsResult = addPoints(winner.id, pointsToAdd, game.seatMap.size + 1);

        await channel.send({
          content: `🏆 الفائز <@${winner.id}> وحصل على نقطتين`,
          files: [{ attachment: buffer, name: 'winner.png' }]
        });

        await deleteWheelFiles(interaction.message.id);
        activeGames.delete(interaction.message.id);
      } else {
        // استدعاء جولة جديدة بعد إقصاء لاعب
        setTimeout(() => startRound(game, msg), 3000);
      }
      return;
    }

    if (interaction.customId.startsWith('kick_')) {
      const game = activeGames.get(interaction.message.id);
      if (!game || !game.picker) {
        return interaction.reply({
          content: '❌ اللعبة غير موجودة أو لا يوجد مختار',
          flags: MessageFlags.Ephemeral
        });
      }

      const data = loadPoints();
      if (interaction.user.id !== game.picker.id) {
        return interaction.reply({ content: '❗ ليس دورك للاختيار.', flags: MessageFlags.Ephemeral });
      }

      await interaction.deferUpdate();
      const seatNum = Number(interaction.customId.split('_')[1]);
      const victim = game.seatMap.get(seatNum);
      if (!victim) return;

      const channel = await client.channels.fetch(game.channelId);
      const victimData = data[victim.id] || { items: [] };

      // التحقق من الحصانة
      if (victimData.items.includes('immunity')) {
        victimData.items = victimData.items.filter(item => item !== 'immunity');
        savePoints(data);

        await channel.send(`🛡️ <@${victim.id}> عنده حصانه محد قده`);

        const msg = await channel.messages.fetch(interaction.message.id);
        return startRound(game, msg);
      }

      // التحقق من إعادة التدوير
      if (victimData.items.includes('reroll')) {
        const rerollRow = new ActionRowBuilder().addComponents(
          new ButtonBuilder()
            .setCustomId(`reroll_${victim.id}`)
            .setLabel('استخدام إعادة التدوير')
            .setStyle(ButtonStyle.Primary)
            .setEmoji('🔄')
        );

        const rerollMsg = await channel.send({
          content: `<@${victim.id}> لديك 15 ثانية لاستخدام عنصر إعادة التدوير!`,
          components: [rerollRow]
        });

        // انتظار 15 ثانية للرد
        try {
          const filter = i => i.customId === `reroll_${victim.id}` && i.user.id === victim.id;
          const response = await rerollMsg.awaitMessageComponent({ filter, time: 15000 });

          if (response) {
            // التحقق من أن التفاعل لا يزال صالحاً
            if (response.isRepliable()) {
              await response.deferUpdate();
            }

            // استخدام العنصر
            victimData.items = victimData.items.filter(item => item !== 'reroll');
            savePoints(data);

            await rerollMsg.edit({
              content: `✅ <@${victim.id}> استخدم عنصر إعادة التدوير!`,
              components: []
            });

            const msg = await channel.messages.fetch(interaction.message.id);
            return startRound(game, msg);
          }
        } catch (error) {
          console.error('خطأ في انتظار استجابة إعادة التدوير:', error);
          try {
            await rerollMsg.edit({
              content: `❌ <@${victim.id}> لم يستخدم عنصر إعادة التدوير في الوقت المحدد.`,
              components: []
            });
          } catch (editError) {
            console.error('خطأ في تحديث رسالة إعادة التدوير:', editError);
          }
        }
      }

      // حذف الضحية من الخريطة
      game.seatMap.delete(seatNum);

      // تحديث رسالة اللعبة وإرسال رسالة منفصلة للإقصاء
      const msg = await channel.messages.fetch(interaction.message.id);
      await msg.edit({
        content: '🎯 تم الاختيار',
        components: []
      });

      await channel.send({
        content: `❌ تم طرد <@${victim.id}> من اللعبة!`
      });

      await channel.send('⏳ سيتم بدء الجولة القادمة خلال ثوان');

      if (game.seatMap.size === 1) {
        // إعلان الفائز
        const winner = [...game.seatMap.values()][0];
        const user = await client.users.fetch(winner.id);
        const buffer = await generateWinnerImage(user);

        const winnerData = data[winner.id] || { items: [] };
        let pointsToAdd = 2; // نقطتين عند الفوز

        if (winnerData.items.includes('double')) {
          pointsToAdd *= 2;
          winnerData.items = winnerData.items.filter(item => item !== 'double');
          savePoints(data);
        }

        const pointsResult = addPoints(winner.id, pointsToAdd, game.seatMap.size + 1);

        await channel.send({
          content: `🏆 الفائز <@${winner.id}> وحصل على نقطتين`,
          files: [{ attachment: buffer, name: 'winner.png' }]
        });

        activeGames.delete(interaction.message.id);
      } else {
        // استدعاء جولة جديدة بعد إقصاء لاعب
        setTimeout(() => startRound(game, msg), 3000);
      }
    }
  } catch (error) {
    console.error('خطأ في معالجة التفاعل:', error);
    try {
      if (!interaction.replied && !interaction.deferred) {
        await interaction.reply({
          content: '❌ حدث خطأ أثناء معالجة طلبك',
          flags: MessageFlags.Ephemeral
        });
      }
    } catch (replyError) {
      console.error('خطأ في الرد على التفاعل:', replyError);
    }
  }
});

async function startRound(game, msg) {
  const seats = Array.from(game.seatMap.keys());
  const names = seats.map(n => game.seatMap.get(n).name);

  // اختيار عشوائي للفائز بعد انتهاء العجلة (وليس قبلها)
  const randomSelectedIndex = Math.floor(Math.random() * seats.length);
  const gifPath = path.join(__dirname, `wheel_${msg.id}.gif`);

  try {
    const finalImagePath = await generateWheelGif(names, randomSelectedIndex, gifPath);
    const channel = await client.channels.fetch(game.channelId);

    const spinMessage = await channel.send({
      content: '🎡 جاري الاختيار...',
      files: [gifPath]
    });

    setTimeout(async () => {
      // الآن نحدد المختار بناءً على نتيجة العجلة العشوائية
      const pickerSeat = seats[randomSelectedIndex];
      const picker = game.seatMap.get(pickerSeat);
      game.picker = picker;

      const data = loadPoints();
      const pickerData = data[picker.id] || { items: [] };
      const hasReroll = pickerData.items.includes('reroll');

      const targetRows = buildTargetRows(game.seatMap, picker.id);
      const kickMessage = await spinMessage.edit({
        content: `🎯 تم اختيار <@${picker.id}>${hasReroll ? '\nلديك عنصر **إعادة تدوير**' : ''}\n\nلديك 30 ثانية لاختيار لاعب`,
        files: [{ attachment: finalImagePath, name: 'wheel_final.png' }],
        components: targetRows
      });

      // إضافة مؤقت 30 ثانية
      setTimeout(async () => {
        try {
          const updatedGame = activeGames.get(spinMessage.id);
          if (updatedGame && updatedGame.picker.id === picker.id) {
            await kickMessage.edit({
              content: '⏰ انتهى الوقت!',
              components: []
            });
            await startRound(game, spinMessage);
          }
        } catch (timeoutError) {
          console.error('خطأ في مؤقت انتهاء الوقت:', timeoutError);
        }
      }, 30000);

      activeGames.delete(msg.id);
      activeGames.set(spinMessage.id, {
        ...game,
        channelId: game.channelId,
        pickerItems: pickerData.items
      });
    }, 3500);
  } catch (error) {
    console.error('حدث خطأ أثناء إنشاء العجلة:', error);
    const channel = await client.channels.fetch(game.channelId);
    channel.send('❌ حدث خطأ أثناء تشغيل العجلة، يرجى المحاولة مرة أخرى');
    await deleteWheelFiles(msg.id);
  }
}




// تسجيل الدخول للبوت
client.login(process.env.TOKEN);
